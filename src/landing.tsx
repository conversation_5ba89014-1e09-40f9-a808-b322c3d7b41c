import { Canvas } from "@react-three/fiber";
import { OrbitControls, useGLTF } from "@react-three/drei";
import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

function Phone() {
  const { scene } = useGLTF("/models/phone.glb");
  const phoneRef = useRef<any>(null);

  useEffect(() => {
    if (!phoneRef.current) return;

    // Animate phone scale and position as user scrolls
    gsap.to(phoneRef.current.scale, {
      x: 3,
      y: 3,
      z: 3,
      ease: "none",
      scrollTrigger: {
        trigger: ".scroll-container",
        start: "top top",
        end: "200% top",
        scrub: true,
      },
    });

    gsap.to(phoneRef.current.position, {
      y: 0.5,
      ease: "none",
      scrollTrigger: {
        trigger: ".scroll-container",
        start: "top top",
        end: "200% top",
        scrub: true,
      },
    });
  }, []);

  return (
    <primitive
      ref={phoneRef}
      object={scene}
      scale={1.5}
      rotation={[0, Math.PI, 0]}
    />
  );
}
useGLTF.preload("/models/phone.glb");

export default function PhoneLanding() {
  const cameraRef = useRef<any>(null);
  const [showSecondScreen, setShowSecondScreen] = useState(false);
  const [zoomProgress, setZoomProgress] = useState(0);

  useEffect(() => {
    if (!cameraRef.current) return;

    const scrollTriggerInstance = ScrollTrigger.create({
      trigger: ".scroll-container",
      start: "top top",
      end: "200% top",
      scrub: true,
      onUpdate: (self) => {
        const progress = self.progress;
        setZoomProgress(progress);

        // Show second screen when zoom is at 90% or more
        if (progress >= 0.9 && !showSecondScreen) {
          setShowSecondScreen(true);
        } else if (progress < 0.9 && showSecondScreen) {
          setShowSecondScreen(false);
        }
      },
    });

    gsap.to(cameraRef.current.position, {
      z: 1.5,
      ease: "none",
      scrollTrigger: {
        trigger: ".scroll-container",
        start: "top top",
        end: "200% top",
        scrub: true,
      },
    });

    return () => {
      scrollTriggerInstance.kill();
    };
  }, [showSecondScreen]);

  return (
    <>
      <div className="scroll-container relative h-[200vh] w-full bg-gradient-to-t from-orange-400 via-black to-black text-white">
        {/* Overlay text */}
        <div className="fixed top-0 left-0 w-full h-full flex items-center justify-between px-10 pointer-events-none">
          <h1 className="text-7xl font-bold">Hello</h1>
          <h1 className="text-7xl font-bold">There</h1>
        </div>

        {/* Three.js Canvas */}
        <Canvas
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
          }}
          camera={{ position: [0, 0, 6], fov: 45 }}
          onCreated={({ camera }) => (cameraRef.current = camera)}
        >
          <ambientLight intensity={0.5} />
          <directionalLight position={[5, 5, 5]} />
          <Phone />
          <OrbitControls enableZoom={false} enableRotate={false} />
        </Canvas>

        {/* Zoom progress indicator */}
        <div className="fixed bottom-4 left-4 text-white pointer-events-none">
          <div className="text-sm">Zoom: {Math.round(zoomProgress * 100)}%</div>
        </div>
      </div>

      {/* Second Screen - appears when phone is fully zoomed */}
      {showSecondScreen && (
        <div className="fixed inset-0 bg-black text-white flex items-center justify-center z-50 transition-opacity duration-500">
          <div className="text-center">
            <h1 className="text-6xl font-bold mb-4">Welcome!</h1>
            <p className="text-xl">Phone is fully zoomed in</p>
            <button
              className="mt-8 px-6 py-3 bg-orange-400 text-black rounded-lg hover:bg-orange-500 transition-colors"
              onClick={() => setShowSecondScreen(false)}
            >
              Go Back
            </button>
          </div>
        </div>
      )}
    </>
  );
}

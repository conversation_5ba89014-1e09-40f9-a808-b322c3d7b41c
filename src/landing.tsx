import { Canvas } from "@react-three/fiber";
import { OrbitControls, useGLTF } from "@react-three/drei";
import { useEffect, useRef } from "react";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

function Phone() {
  const { scene } = useGLTF("/models/phone.glb");
  return <primitive object={scene} scale={1.5} rotation={[0, Math.PI, 0]} />; // rotates 180° around Y axis />;
}
useGLTF.preload("/models/phone.glb");

export default function PhoneLanding() {
  const cameraRef = useRef<any>(null);

  useEffect(() => {
    if (!cameraRef.current) return;

    gsap.to(cameraRef.current.position, {
      z: 1.5,
      ease: "none",
      scrollTrigger: {
        trigger: ".scroll-container",
        start: "top top",
        end: "200% top",
        scrub: true,
      },
    });
  }, []);

  return (
    <div className="scroll-container relative h-[200vh] w-full bg-gradient-to-r from-orange-400 via-black to-black text-white">
      {/* Overlay text */}
      <div className="fixed top-0 left-0 w-full h-full flex items-center justify-between px-10 pointer-events-none">
        <h1 className="text-4xl font-bold">hello</h1>
        <h1 className="text-4xl font-bold">there</h1>
      </div>

      {/* Three.js Canvas */}
      <Canvas
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
        }}
        camera={{ position: [0, 0, 6], fov: 45 }}
        onCreated={({ camera }) => (cameraRef.current = camera)}
      >
        <ambientLight intensity={0.5} />
        <directionalLight position={[5, 5, 5]} />
        <Phone />
        <OrbitControls enableZoom={false} enableRotate={false} />
      </Canvas>
    </div>
  );
}
